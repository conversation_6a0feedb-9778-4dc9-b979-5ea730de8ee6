{"name": "upkeeppro-backend", "version": "1.0.0", "description": "UpKeepPro Backend Server", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.6", "multer": "^2.0.0", "node-cron": "^4.0.7", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.9"}}