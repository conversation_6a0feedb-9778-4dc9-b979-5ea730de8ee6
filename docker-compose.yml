version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGO_URI=mongodb://mongo:27017/UpKeepPro
      - JWT_SECRET=ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=
      - FRONTEND_URL=http://localhost:5000
    depends_on:
      - mongo
    volumes:
      - uploads:/app/uploads
      - backend_uploads:/app/backend/uploads
    restart: unless-stopped

  mongo:
    image: mongo:7-jammy
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped
    command: mongod --quiet --logpath /dev/null

volumes:
  mongo_data:
  uploads:
  backend_uploads:
