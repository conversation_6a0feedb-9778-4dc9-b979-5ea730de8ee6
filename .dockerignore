# Dependencies
node_modules
frontend/node_modules
backend/node_modules

# Build outputs
frontend/dist
frontend/build

# Development files
.git
.gitignore
README.md
*.md

# Environment files
.env.local
.env.development
.env.test

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/
